# Create New Message Schedule — Wireframe Specification

This page is a **single HTML page** used to configure and schedule outbound messages. The 4 screenshots show the **same page** in different interaction states (template selection and outlet selection actions). Below is a complete description of the layout, positions, inputs, and actions.

---

## A. Page <PERSON>er (Top Bar)

- **Page title (left):** `Create New Message Schedule` (static label).
- **Save button (far right):** `Save` (primary action).

### A1. Basic Info Row (3 columns)

1. **Title** *(required, left column)*  
   - Type: single-line text input.  
   - Example value: “Thông báo CTKM Base tháng 6.2025”.

2. **Message Template** *(required, center column)*  
   - Type: dropdown/select.  
   - Examples:  
     - “Thông báo CTKM 2 mức” (base template).  
     - “Thông báo tích lũy” (accumulation template).  
   - **Selecting a template changes the parameter table in Section B.**

3. **Sending Date** *(required, right column)*  
   - Type: date-time picker.  
   - Example: `2025-07-01 10:00`.

---

## B. Message Parameters Setting (Main block)

### B1. Preview blocks (two read-only text areas, 2 columns)

- **Message Format** *(left)* — read-only multiline preview showing message with placeholders (e.g., `Xin chào <customer_name>…`).
- **Sample** *(right)* — read-only multiline sample message after mapping (e.g., “Xin chào Tạp hóa Anh Thư…”).

### B2. Parameter Mapping Table (full width)

A grid with **columns**:  
**Parameter | Description | Data type | Data value / Item mapping**  

> The **rows differ by message template**:
>
> #### When template = “Thông báo CTKM 2 mức”
> - `customer_name` | Tên khách hàng | **Data item** | `outlet_name` (select)
> - `customer_code` | Mã khách hàng | **Data item** | `outlet_code` (select)
> - `promotion_name` | Tên CTKM | **Input value** | free text (e.g., “KM Lipoviton Honey tháng 6”)
> - `sell_qty_1` | Số lượng bậc 1 | **Input value** | free text (e.g., “2 thùng”)  
> - `free_qty_1` | Số lượng KM 1 | **Input value** | free text (e.g., “3 lon”)
> - `sell_qty_2` | Số lượng bậc 2 | **Input value** | free text (e.g., “5 thùng”)
> - `free_qty_2` | Số lượng KM 2 | **Input value** | free text (e.g., “9 lon”)
> - `time_period` | Thời gian áp dụng | **Input value** | free text (e.g., “1/6/2025 – 30/6/2025”)
> - `remarks` | Ghi chú thêm | **Input value** | free text (e.g., “Không áp dụng đồng thời…”)
>
> #### When template = “Thông báo tích lũy”
> - `customer_name` | Tên khách hàng | **Data item** | `outlet_name` (select)
> - `customer_code` | Mã khách hàng | **Data item** | `outlet_code` (select)
> - `promotion_name` | Tên CTKM | **Input value** | free text (e.g., “Tích lũy Lipoviton Honey Quý 2 2025”)
> - `registered_vol` | Sản lượng đăng ký | **Data item** | `accum_plan_vol` (select)
> - `actual_vol` | Sản lượng đã thực hiện | **Data item** | `accum_actual_vol` (select)
> - `gap_vol` | Sản lượng còn thiếu | **Data item** | `accum_gap_vol` (select)
> - `end_date` | Ngày kết thúc chương trình | **Input value** | free text or date (e.g., “30/6/2025”)
> - `reward` | Thưởng dự kiến | **Data item** | `accum_free_qty` (select)

#### B3. Inline action
- **Preview message** (button) — shows preview using current mappings.

---

## C. Received Outlets (Targeting block)

### C1. Mode selector (top-left of block)
- **Radio**: `Filter outlets` | `Import outlets` (mutually exclusive).

---

### C2. Filter Outlets mode (state shown in screenshots 1, 2, and 4)

**Inputs (left-to-right layout across two rows):**

1. **All outlets with Zalo verified** — checkbox.

2. **Organization** — dropdown with **tree & multiselect**.  
   - Opens a modal/popup with checkboxes for nodes and leaves.  
   - Example nodes: `North 1`, `North 2`, `North Central`, `Central`, `South Central`, `Highland`…  
   - Example leaves (under `North Central`, etc.): `Giang Thắng`, `Gia Bảo Dương`, `Huế Lùi`, `Dự Phòng Phát`, `Phong Dương`, `Thắng Bình Nghi Sơn`…  
   - **Modal actions:** `Confirm`, `Select All`, `Clear`.

3. **Location** — free-text multi-select input (comma-separated).  
   - Example typed values: `Thanh Hóa, Quảng Trị, Đà Nẵng`.

4. **Outlet Type** — dropdown multi-select.  
   - Examples: `Dealer`, `Semi`, `Retail OFF`, `Semi Retail`…

5. **Sales volume of** — product dropdown multi-select.  
   - Default: `All products`.  
   - Example options: `Honey`, `Tongkat Ali`.

6. **in last** — duration dropdown.  
   - Options: `1 month` *(default)*, `3 month`, `6 month`, `12 month`.

7. **(Comparator)** — dropdown placed right after duration.  
   - Options: `Greater Than Or Equal To`, `Between`.

8. **Trays** — numeric input (one value when using “Greater Than Or Equal To”; **two** values if “Between” is selected).

**Actions (right / bottom of the filter row):**
- **Apply** (button).

**Outlet List (full-width table under the filters):**
- **Buttons above table (left to right):** `Clear List`, `Export`.
- **Columns:** `Outlet Code | Outlet Name | Outlet Type | Region | Province | Ward`.
- **Example rows:** (as seen) e.g., `QBI007486 | Mini Mart Tấn Lợi | Semi Retail | North Central Coast | Quảng Bình | Xã Quảng Đông` and others.

---

### C3. Import Outlets mode (state shown in screenshot 3)

**Inputs:**
1. **File uploader** — drag & drop area, accepts `.xlsx`.  
   - Help text: “Accepted file format: .xlsx — Limit 10,000 lines of data”.  
   - Link: `Download template`.
2. **Apply** (button).

**Outlet List (same table as in Filter mode):**  
Appears **after** a successful import; shows the same columns as above.  
Example file name displayed under dropzone: `accumulation_outlets_202506.xlsx`.

---

## D. Global Elements & Behaviors

- Required fields are indicated with a small red dot next to the label (e.g., **Title**, **Message Template**, **Sending Date**; and column-level requirement marks inside the parameter table when editing).
- Changing **Message Template** updates the **Message Format**, **Sample**, and **Parameter Mapping Table** (Section B2).
- The page does **not** navigate; all actions occur inline: select dropdowns, open modal for Organization, preview message, filter/apply, upload, and save.
- Persistent bottom area: **Outlet List table** remains visible on this page in either mode.

---

## E. Complete List of Inputs & Controls (for QA)

**Header:**  
- Text input: `Title` *(required)*  
- Select: `Message Template` *(required)*  
- Date-time picker: `Sending Date` *(required)*  
- Button: `Save`

**Message Parameters Setting:**  
- Read-only text areas: `Message Format`, `Sample`  
- Table rows (template-dependent, listed exhaustively above) with per-row controls:  
  - **Data type**: `Data item` | `Input value` (per row)  
  - **Data value / Item mapping**: dropdown when `Data item`, text input when `Input value`  
- Button: `Preview message`

**Received Outlets:**  
- Radio: `Filter outlets` | `Import outlets`  
- (Filter mode) Checkbox: `All outlets with Zalo verified`  
- (Filter mode) Organization: dropdown → tree multiselect modal with checkboxes and actions `Confirm | Select All | Clear`  
- (Filter mode) Text/multiselect input: `Location`  
- (Filter mode) Select: `Outlet Type` (multi)  
- (Filter mode) Select: `Sales volume of` (multi)  
- (Filter mode) Select: `in last` (1/3/6/12 month)  
- (Filter mode) Select: comparator `Greater Than Or Equal To | Between`  
- (Filter mode) Number input(s): `Trays`  
- (Filter mode) Button: `Apply`  
- (Import mode) File uploader: `.xlsx` (10,000-line limit) + link `Download template`  
- (Import mode) Button: `Apply`  
- (Both modes) Buttons above table: `Clear List | Export`  
- (Both modes) Table with columns: `Outlet Code | Outlet Name | Outlet Type | Region | Province | Ward`

---

**Note:** The 4 images only show **different states** (template switched; filter vs import; dropdown expanded); the underlying layout and field set remain as documented above.
