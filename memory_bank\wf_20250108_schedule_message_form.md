# Workflow: Tạo Form Lập Lịch Gửi Tin Nhắn Tự Động

**Ng<PERSON><PERSON> thực hiện:** 08/01/2025  
**File được tạo:** `schedule_message_form.html`

## Mô tả dự án
Tạo một trang HTML hoàn chỉnh cho form lập lịch gửi tin nhắn tự động dựa trên wireframe 4.png và các wireframe hỗ trợ khác trong thư mục memory_bank.

## Các thành phần đã implement

### 1. Cấu trúc HTML
- ✅ Sử dụng Bootstrap 5 cho responsive design
- ✅ Layout rõ ràng, dễ sử dụng trên desktop và mobile
- ✅ Semantic HTML với accessibility tốt
- ✅ Icons từ Bootstrap Icons

### 2. Phần thông tin cơ bản
- ✅ Input "Tiêu đề" (required)
- ✅ Select "Template mẫu" với 5 template có sẵn:
  - <PERSON><PERSON><PERSON> mừng khách hàng mới
  - <PERSON><PERSON><PERSON><PERSON>n mãi đặc biệt
  - Nhắc nhở thanh toán
  - <PERSON><PERSON><PERSON> mừng sinh nhật
  - Khảo sát hài lòng
- ✅ Input "Ngày gửi" (datetime-local, required)

### 3. Phần nội dung tin nhắn tùy biến
- ✅ Textarea cho nội dung tin nhắn (required)
- ✅ Dropdown select với 7 biến có sẵn:
  - {ten_khach_hang}
  - {so_dien_thoai}
  - {dia_chi}
  - {email}
  - {ngay_sinh}
  - {ma_khach_hang}
  - {diem_tich_luy}
- ✅ Text input cho biến tùy chỉnh
- ✅ Button "Chèn biến" với chức năng chèn vào vị trí cursor
- ✅ Preview area hiển thị nội dung với biến được thay thế bằng dữ liệu mẫu

### 4. Phần "Received Outlets" (Bộ lọc khách hàng)
- ✅ **Organization**: Select tree với checkbox (3 cấp: Tất cả → Thành phố → Khu vực)
- ✅ **Location**: Select tree với checkbox (3 cấp: Tất cả → Loại địa điểm → Chi tiết)
- ✅ **Outlet Type**: Select tree với checkbox (2 cấp: Tất cả → Loại hình)
- ✅ **Import Outlets**: 
  - Checkbox "Import outlets từ file"
  - File upload area với drag & drop support
  - Hỗ trợ Excel (.xlsx, .xls) và CSV (.csv)
  - Hiển thị tên file đã chọn

### 5. Chức năng JavaScript
- ✅ **Select tree functionality:**
  - Expand/collapse với icon toggle
  - Parent-child checkbox relationship
  - Indeterminate state cho parent khi một số child được chọn
  - Select all/none khi click parent
- ✅ **Template system:**
  - Auto-fill nội dung khi chọn template
  - 5 template mẫu có sẵn
- ✅ **Variable insertion:**
  - Chèn biến vào vị trí cursor trong textarea
  - Hỗ trợ cả biến có sẵn và biến tùy chỉnh
  - Reset input sau khi chèn
- ✅ **Real-time preview:**
  - Cập nhật preview khi thay đổi nội dung
  - Thay thế biến bằng dữ liệu mẫu
  - Highlight biến bằng CSS styling
- ✅ **File upload:**
  - Toggle hiện/ẩn khi check "Import outlets"
  - Drag & drop support
  - Hiển thị tên file đã chọn
- ✅ **Form validation:**
  - HTML5 validation cho required fields
  - Bootstrap validation styling
  - Thu thập và log dữ liệu form khi submit
- ✅ **Preview modal:**
  - Hiển thị preview đầy đủ trong modal
  - Format ngày giờ theo locale Việt Nam

### 6. Styling và UX
- ✅ Custom CSS cho select tree
- ✅ Responsive design cho mobile/tablet
- ✅ Hover effects và transitions
- ✅ Color coding cho các section
- ✅ File upload area với visual feedback
- ✅ Variable tags styling trong preview

## Dữ liệu mẫu đã tạo

### Templates
1. **welcome**: Chào mừng khách hàng mới
2. **promotion**: Khuyến mãi đặc biệt  
3. **reminder**: Nhắc nhở thanh toán
4. **birthday**: Chúc mừng sinh nhật
5. **survey**: Khảo sát hài lòng

### Organization Tree
- Tất cả tổ chức
  - Hà Nội (Trung tâm, Phía Bắc)
  - TP.HCM (Quận 1, Quận 7)

### Location Tree
- Tất cả địa điểm
  - Cửa hàng (Cửa hàng chính, Chi nhánh)
  - Kho hàng

### Outlet Type Tree
- Tất cả loại hình
  - Bán lẻ
  - Bán sỉ
  - Trực tuyến

### Sample Data cho Preview
- Nguyễn Văn A, 0123456789, địa chỉ mẫu, email mẫu, etc.

## Tính năng nổi bật
1. **Fully functional** - Tất cả chức năng đều hoạt động
2. **Responsive design** - Tương thích mobile/tablet
3. **Accessibility** - Labels, ARIA attributes
4. **User-friendly** - Intuitive interface, clear feedback
5. **Extensible** - Dễ dàng thêm template, biến, tree items mới

## Cách sử dụng
1. Mở file `schedule_message_form.html` trong browser
2. Điền thông tin cơ bản (tiêu đề, template, ngày gửi)
3. Tùy chỉnh nội dung tin nhắn và chèn biến
4. Chọn đối tượng nhận trong Received Outlets
5. Tùy chọn import file danh sách khách hàng
6. Xem trước và submit form

## Kết quả
✅ **Hoàn thành 100%** - File HTML functional, responsive, và sẵn sàng sử dụng
