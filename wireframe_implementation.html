<!doctype html>
<html lang="vi">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Wireframe Implementation (1–11)</title>
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- j<PERSON><PERSON>y (ưu tiên theo yêu cầu) -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <!-- Bootstrap 5 JS (Bundle includes <PERSON><PERSON>) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <style>
    /* <PERSON><PERSON>t s<PERSON> tinh chỉnh để trình bày gọn gàng */
    body{background:#f7f8fa}
    .wf-section{padding:24px 0;border-top:1px solid #e9ecef}
    .wf-header{position:sticky;top:0;background:#fff;z-index:1030;border-bottom:1px solid #e9ecef}
    .tree-dropdown .dropdown-menu{max-height:280px;overflow:auto;width:320px}
    .tree-node{display:flex;align-items:center;gap:6px;cursor:pointer;padding:4px 6px;border-radius:6px}
    .tree-node:hover{background:#f1f3f5}
    .tree-children{margin-left:18px;margin-top:4px}
    .caret{user-select:none;font-size:12px;width:16px;text-align:center;color:#6c757d}
    .table-actions .btn{padding:2px 8px}
    .required::after{content:" *"; color:#dc3545}
  </style>
</head>
<body>
  <!-- Thanh điều hướng nhanh tới từng wireframe -->
  <div class="wf-header py-2">
    <div class="container-fluid">
      <div class="d-flex flex-wrap align-items-center gap-2">
        <span class="fw-semibold me-2">Wireframes:</span>
        <div class="btn-group btn-group-sm" role="group" aria-label="Quick Nav">
          <a class="btn btn-outline-primary" href="#wf1">1</a>
          <a class="btn btn-outline-primary" href="#wf2">2</a>
          <a class="btn btn-outline-primary" href="#wf3">3</a>
          <a class="btn btn-outline-primary" href="#wf4">4</a>
          <a class="btn btn-outline-primary" href="#wf5">5</a>
          <a class="btn btn-outline-primary" href="#wf6">6</a>
          <a class="btn btn-outline-primary" href="#wf7">7</a>
          <a class="btn btn-outline-primary" href="#wf8">8</a>
          <a class="btn btn-outline-primary" href="#wf9">9</a>
          <a class="btn btn-outline-primary" href="#wf10">10</a>
          <a class="btn btn-outline-primary" href="#wf11">11</a>
        </div>
      </div>
    </div>
  </div>

  <div class="container-fluid">
    <!-- WF1: Bộ lọc + Bảng kết quả -->
    <section id="wf1" class="wf-section">
      <h3 class="mb-3">Wireframe 1: Bộ lọc và bảng</h3>
      <div class="row g-3">
        <div class="col-12 col-lg-4">
          <div class="card shadow-sm">
            <div class="card-body">
              <h6 class="card-title mb-3">Bộ lọc</h6>
              <div class="mb-3">
                <label class="form-label required">Tìm kiếm</label>
                <input type="text" class="form-control" id="wf1Search" placeholder="Nhập từ khóa">
              </div>
              <div class="mb-3">
                <label class="form-label">Trạng thái</label>
                <select id="wf1Status" class="form-select"></select>
              </div>
              <!-- Select Tree mẫu dùng chung -->
              <div class="mb-3">
                <label class="form-label">Nhóm (Select Tree)</label>
                <div class="dropdown tree-dropdown" data-tree-for="#wf1Group">
                  <button class="btn btn-outline-secondary dropdown-toggle w-100 text-start" data-bs-toggle="dropdown" aria-expanded="false" id="wf1TreeBtn">Chọn nhóm</button>
                  <div class="dropdown-menu p-2">
                    <div class="form-floating mb-2">
                      <input type="text" class="form-control form-control-sm" id="treeSearch" placeholder="Tìm">
                      <label for="treeSearch">Tìm nhanh</label>
                    </div>
                    <div class="tree-root"></div>
                  </div>
                </div>
                <input type="hidden" id="wf1Group" />
              </div>
              <div class="mb-3">
                <label class="form-label">Khoảng thời gian</label>
                <div class="row g-2">
                  <div class="col"><input type="date" class="form-control" id="wf1From"></div>
                  <div class="col"><input type="date" class="form-control" id="wf1To"></div>
                </div>
              </div>
              <div class="form-check mb-2"><input class="form-check-input" type="checkbox" id="wf1OnlyMine"><label class="form-check-label" for="wf1OnlyMine">Chỉ của tôi</label></div>
              <div class="d-flex gap-2">
                <button class="btn btn-primary" id="wf1Apply">Áp dụng</button>
                <button class="btn btn-outline-secondary" id="wf1Reset">Làm mới</button>
              </div>
            </div>
          </div>
        </div>
        <div class="col-12 col-lg-8">
          <div class="card shadow-sm">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Kết quả</h6>
                <div class="d-flex gap-2">
                  <button class="btn btn-success btn-sm" id="wf1Add">Thêm</button>
                  <button class="btn btn-warning btn-sm" id="wf1Export">Xuất</button>
                </div>
              </div>
              <div class="table-responsive">
                <table class="table table-sm table-striped align-middle">
                  <thead><tr><th>#</th><th>Tên</th><th>Nhóm</th><th>Trạng thái</th><th>Ngày</th><th class="text-end">Thao tác</th></tr></thead>
                  <tbody id="wf1TableBody"></tbody>
                </table>
              </div>
              <nav><ul class="pagination pagination-sm mb-0" id="wf1Pagination"></ul></nav>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- WF2: Form chi tiết -->
    <section id="wf2" class="wf-section">
      <h3 class="mb-3">Wireframe 2: Form chi tiết</h3>
      <form id="wf2Form" class="card shadow-sm p-3">
        <div class="row g-3">
          <div class="col-sm-6">
            <label class="form-label required">Tiêu đề</label>
            <input type="text" class="form-control" id="wf2Title" required>
          </div>
          <div class="col-sm-6">
            <label class="form-label">Loại</label>
            <select id="wf2Type" class="form-select"></select>
          </div>
          <div class="col-sm-6">
            <label class="form-label">Kênh</label>
            <div class="d-flex gap-3">
              <div class="form-check"><input class="form-check-input" type="radio" name="wf2Channel" id="wf2Zalo" value="zalo" checked><label class="form-check-label" for="wf2Zalo">Zalo</label></div>
              <div class="form-check"><input class="form-check-input" type="radio" name="wf2Channel" id="wf2Fb" value="facebook"><label class="form-check-label" for="wf2Fb">Facebook</label></div>
            </div>
          </div>
          <div class="col-sm-6">
            <label class="form-label">Kích hoạt</label>
            <div class="form-check form-switch"><input class="form-check-input" type="checkbox" id="wf2Active" checked><label class="form-check-label" for="wf2Active">Bật/Tắt</label></div>
          </div>
          <div class="col-12">
            <label class="form-label">Nội dung</label>
            <textarea class="form-control" id="wf2Content" rows="3" placeholder="Nhập nội dung..."></textarea>
          </div>
          <div class="col-12 d-flex gap-2">
            <button class="btn btn-primary" type="submit">Lưu</button>
            <button class="btn btn-outline-secondary" type="button" id="wf2Draft">Lưu nháp</button>
            <button class="btn btn-danger" type="button" id="wf2Delete">Xóa</button>
          </div>
        </div>
      </form>
    </section>

    <!-- WF3: Bộ lọc nâng cao với checkbox group -->
    <section id="wf3" class="wf-section">
      <h3 class="mb-3">Wireframe 3: Bộ lọc nâng cao</h3>
      <div class="card p-3 shadow-sm">
        <div class="row g-3 align-items-end">
          <div class="col-md-3">
            <label class="form-label">Danh mục</label>
            <select class="form-select" id="wf3Cat"></select>
          </div>
          <div class="col-md-6">
            <label class="form-label">Thuộc tính</label>
            <div class="d-flex flex-wrap gap-3" id="wf3Attrs"></div>
          </div>
          <div class="col-md-3 text-md-end">
            <button class="btn btn-primary" id="wf3Search">Tìm kiếm</button>
          </div>
        </div>
      </div>
    </section>

    <!-- WF4: Danh sách + Modal -->
    <section id="wf4" class="wf-section">
      <h3 class="mb-3">Wireframe 4: Danh sách và Modal</h3>
      <div class="d-flex gap-2 mb-2"><button class="btn btn-success" id="wf4OpenModal">Tạo mới</button><button class="btn btn-outline-secondary" id="wf4Refresh">Làm mới</button></div>
      <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle mb-0">
          <thead class="table-light"><tr><th>#</th><th>Tên</th><th>Mô tả</th><th>Thao tác</th></tr></thead>
          <tbody id="wf4Body"></tbody>
        </table>
      </div>
    </section>

    <!-- WF5: Thống kê (cards) -->
    <section id="wf5" class="wf-section">
      <h3 class="mb-3">Wireframe 5: Thống kê</h3>
      <div class="row g-3" id="wf5Stats"></div>
    </section>

    <!-- WF6: Bảng có chọn nhiều dòng -->
    <section id="wf6" class="wf-section">
      <h3 class="mb-3">Wireframe 6: Bảng chọn nhiều</h3>
      <div class="d-flex gap-2 mb-2"><button class="btn btn-outline-primary btn-sm" id="wf6SelectAll">Chọn tất</button><button class="btn btn-outline-secondary btn-sm" id="wf6UnselectAll">Bỏ chọn</button><button class="btn btn-danger btn-sm" id="wf6BulkDelete">Xóa đã chọn</button></div>
      <div class="table-responsive">
        <table class="table table-sm table-striped align-middle">
          <thead><tr><th><input type="checkbox" id="wf6CheckAll"></th><th>Tên</th><th>Loại</th><th>Ngày</th></tr></thead>
          <tbody id="wf6Body"></tbody>
        </table>
      </div>
    </section>

    <!-- WF7: Form nhiều bước (giản lược) -->
    <section id="wf7" class="wf-section">
      <h3 class="mb-3">Wireframe 7: Form nhiều bước</h3>
      <div class="card p-3 shadow-sm">
        <div class="mb-3">
          <div class="btn-group" role="group">
            <button class="btn btn-outline-primary active" data-step="1">Bước 1</button>
            <button class="btn btn-outline-primary" data-step="2">Bước 2</button>
            <button class="btn btn-outline-primary" data-step="3">Xác nhận</button>
          </div>
        </div>
        <div id="wf7Steps">
          <div class="step" data-step="1">
            <label class="form-label">Tên chiến dịch</label><input class="form-control" id="wf7Name"/>
          </div>
          <div class="step d-none" data-step="2">
            <label class="form-label">Thời gian chạy</label><div class="row g-2"><div class="col"><input type="date" class="form-control" id="wf7From"></div><div class="col"><input type="date" class="form-control" id="wf7To"></div></div>
          </div>
          <div class="step d-none" data-step="3">
            <div class="alert alert-info">Vui lòng kiểm tra thông tin trước khi xác nhận.</div>
            <button class="btn btn-primary" id="wf7Confirm">Xác nhận</button>
          </div>
        </div>
      </div>
    </section>

    <!-- WF8: Upload đơn giản -->
    <section id="wf8" class="wf-section">
      <h3 class="mb-3">Wireframe 8: Upload</h3>
      <div class="card p-3 shadow-sm">
        <div class="mb-3"><label class="form-label">Chọn tệp</label><input class="form-control" type="file" id="wf8File" multiple></div>
        <button class="btn btn-primary" id="wf8Upload">Tải lên</button>
        <ul class="mt-3" id="wf8List"></ul>
      </div>
    </section>

    <!-- WF9: Tabs -->
    <section id="wf9" class="wf-section">
      <h3 class="mb-3">Wireframe 9: Tabs</h3>
      <ul class="nav nav-tabs" id="wf9Tabs" role="tablist">
        <li class="nav-item" role="presentation"><button class="nav-link active" data-bs-toggle="tab" data-bs-target="#wf9a" type="button" role="tab">Tổng quan</button></li>
        <li class="nav-item" role="presentation"><button class="nav-link" data-bs-toggle="tab" data-bs-target="#wf9b" type="button" role="tab">Cấu hình</button></li>
        <li class="nav-item" role="presentation"><button class="nav-link" data-bs-toggle="tab" data-bs-target="#wf9c" type="button" role="tab">Lịch sử</button></li>
      </ul>
      <div class="tab-content border border-top-0 p-3 bg-white shadow-sm">
        <div class="tab-pane fade show active" id="wf9a" role="tabpanel">Nội dung tổng quan...</div>
        <div class="tab-pane fade" id="wf9b" role="tabpanel">Một vài cấu hình tại đây...</div>
        <div class="tab-pane fade" id="wf9c" role="tabpanel">Bảng lịch sử hiển thị tại đây...</div>
      </div>
    </section>

    <!-- WF10: Toast/Alert -->
    <section id="wf10" class="wf-section">
      <h3 class="mb-3">Wireframe 10: Thông báo</h3>
      <div class="d-flex gap-2">
        <button class="btn btn-success" id="wf10ToastBtn">Hiện Toast</button>
        <button class="btn btn-outline-warning" id="wf10AlertBtn">Hiện Alert</button>
      </div>
      <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1080">
        <div id="wf10Toast" class="toast align-items-center" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="d-flex"><div class="toast-body">Thao tác đã thực hiện thành công.</div><button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button></div>
        </div>
      </div>
    </section>

    <!-- WF11: Footer / Actions -->
    <section id="wf11" class="wf-section mb-5">
      <h3 class="mb-3">Wireframe 11: Tác vụ cuối trang</h3>
      <div class="d-flex gap-2 flex-wrap">
        <button class="btn btn-primary">Lưu thay đổi</button>
        <button class="btn btn-outline-secondary">Hủy</button>
        <button class="btn btn-danger">Xóa toàn bộ</button>
      </div>
    </section>
  </div>

  <!-- Modal dùng chung cho WF4 -->
  <div class="modal fade" id="wf4Modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header"><h5 class="modal-title">Tạo mới</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div>
        <div class="modal-body">
          <div class="mb-3"><label class="form-label">Tên</label><input class="form-control" id="wf4Name"></div>
          <div class="mb-3"><label class="form-label">Mô tả</label><textarea class="form-control" id="wf4Desc" rows="3"></textarea></div>
        </div>
        <div class="modal-footer"><button class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button><button class="btn btn-primary" id="wf4Save">Lưu</button></div>
      </div>
    </div>
  </div>

<script>
// DỮ LIỆU MẪU
const sampleStatuses = [
  {value:'all', text:'Tất cả'}, {value:'active', text:'Đang hoạt động'}, {value:'paused', text:'Tạm dừng'}, {value:'draft', text:'Nháp'}
];
const sampleTypes = [
  {value:'post', text:'Bài đăng'}, {value:'message', text:'Tin nhắn'}, {value:'event', text:'Sự kiện'}
];
const sampleTree = [
  { id: 'grp1', name: 'Nhóm A', children:[{id:'grp1-1', name:'Nhóm A1'},{id:'grp1-2', name:'Nhóm A2'}] },
  { id: 'grp2', name: 'Nhóm B', children:[{id:'grp2-1', name:'Nhóm B1', children:[{id:'grp2-1-1', name:'Nhóm B1.1'}]}] },
  { id: 'grp3', name: 'Nhóm C' }
];
const tableData = Array.from({length:28}, (_,i)=>({id:i+1, name:`Mục ${i+1}`, group:['Nhóm A','Nhóm B','Nhóm C'][i%3], status: sampleStatuses[(i%3)+1].text, date: `2025-08-${(i%28+1).toString().padStart(2,'0')}`}));

// TIỆN ÍCH CHUNG
function fillSelect($sel, items){ $sel.empty(); items.forEach(o=>$sel.append(`<option value="${o.value}">${o.text}</option>`)); }
function paginate(arr, page, size){ const start=(page-1)*size; return arr.slice(start, start+size); }
function renderPagination($ul, total, page, size, onClick){ const pages=Math.ceil(total/size); $ul.empty(); for(let p=1;p<=pages;p++){ $ul.append(`<li class="page-item ${p===page?'active':''}"><a class="page-link" href="#">${p}</a></li>`); } $ul.find('a').on('click', function(e){ e.preventDefault(); onClick(parseInt($(this).text())); }); }

// SELECT TREE (dựa trên dropdown + UL lồng nhau)
function buildTreeHtml(nodes){
  if(!nodes) return '';
  let html = '<ul class="list-unstyled mb-0">';
  nodes.forEach(n=>{
    const has = Array.isArray(n.children) && n.children.length;
    html += `<li>
      <div class="tree-node" data-id="${n.id}" data-name="${n.name}">
        <span class="caret">${has?'▸':''}</span>
        <span class="label flex-grow-1">${n.name}</span>
      </div>
      ${has?`<div class="tree-children d-none">${buildTreeHtml(n.children)}</div>`:''}
    </li>`;
  });
  html += '</ul>';
  return html;
}
function initTree($dropdown, data){
  const $root = $dropdown.find('.tree-root');
  $root.html(buildTreeHtml(data));
  // Toggle expand/collapse
  $root.on('click', '.tree-node .caret', function(e){ e.stopPropagation(); const $li=$(this).closest('li'); $li.children('.tree-children').toggleClass('d-none'); $(this).text($(this).text()==='▸'?'▾':'▸'); });
  // Select node
  $root.on('click', '.tree-node .label', function(){ const $node=$(this).closest('.tree-node'); const id=$node.data('id'); const name=$node.data('name'); const target=$dropdown.data('tree-for'); $(target).val(id); $dropdown.find('button').text(name); $dropdown.find('.dropdown-toggle').dropdown('hide'); });
  // Search in tree
  const $search = $('#treeSearch');
  $search.on('input', function(){ const q=$(this).val().toLowerCase(); $root.find('.tree-node').each(function(){ const name=$(this).data('name').toLowerCase(); const match=name.includes(q); $(this).closest('li').toggle(match || q===''); }); });
}

$(function(){
  // Khởi tạo select box
  fillSelect($('#wf1Status'), sampleStatuses);
  fillSelect($('#wf2Type'), sampleTypes);
  fillSelect($('#wf3Cat'), [ {value:'all',text:'Tất cả'}, {value:'news',text:'Tin tức'}, {value:'promo',text:'Khuyến mãi'} ]);

  // Khởi tạo thuộc tính (checkbox group)
  const attrs=['Nổi bật','Ưu tiên','Công khai','Có lịch'];
  attrs.forEach((a,i)=>$('#wf3Attrs').append(`<div class="form-check"><input class="form-check-input" type="checkbox" id="wf3Attr${i}"><label class="form-check-label" for="wf3Attr${i}">${a}</label></div>`));

  // Khởi tạo Select Tree mẫu
  initTree($('.tree-dropdown'), sampleTree);

  // WF1: Render bảng + phân trang + filter cơ bản
  const pageSize=6; let currentPage=1; let currentRows=[...tableData];
  function applyFilter(){ const q=$('#wf1Search').val().toLowerCase(); const st=$('#wf1Status').val(); const grp=$('#wf1Group').val(); currentRows=tableData.filter(r=> (q===''||r.name.toLowerCase().includes(q)) && (st==='all'||r.status.toLowerCase().includes(st)) && (!grp || r.group.toLowerCase().includes(grp.slice(0,5).toLowerCase())) ); currentPage=1; render(); }
  function render(){ const rows=paginate(currentRows, currentPage, pageSize); const $tb=$('#wf1TableBody'); $tb.empty(); rows.forEach(r=>{ $tb.append(`<tr><td>${r.id}</td><td>${r.name}</td><td>${r.group}</td><td>${r.status}</td><td>${r.date}</td><td class="text-end table-actions"><button class="btn btn-sm btn-outline-primary me-1">Sửa</button><button class="btn btn-sm btn-outline-danger">Xóa</button></td></tr>`); }); renderPagination($('#wf1Pagination'), currentRows.length, currentPage, pageSize, (p)=>{ currentPage=p; render(); }); }
  $('#wf1Apply').on('click', applyFilter);
  $('#wf1Reset').on('click', function(){ $('#wf1Search').val(''); $('#wf1Status').val('all'); $('#wf1Group').val(''); $('#wf1TreeBtn').text('Chọn nhóm'); applyFilter(); });
  render();

  // WF2: Submit + validate đơn giản
  $('#wf2Form').on('submit', function(e){ e.preventDefault(); if(!$('#wf2Title').val().trim()){ alert('Vui lòng nhập Tiêu đề'); return; } alert('Đã lưu thành công!'); });
  $('#wf2Draft').on('click', ()=>alert('Đã lưu nháp.'));
  $('#wf2Delete').on('click', ()=>confirm('Bạn chắc chắn muốn xóa?') && alert('Đã xóa.'));

  // WF3: Search
  $('#wf3Search').on('click', function(){ const cat=$('#wf3Cat').val(); const chosen=$('#wf3Attrs input:checked').map((_,el)=>$(el).next('label').text()).get(); alert('Tìm với Danh mục='+cat+'; Thuộc tính='+chosen.join(', ')); });

  // WF4: List + Modal
  let wf4Items = [ {name:'Mục A', desc:'Mô tả A'}, {name:'Mục B', desc:'Mô tả B'} ];
  function renderWf4(){ const $b=$('#wf4Body').empty(); wf4Items.forEach((it,idx)=> $b.append(`<tr><td>${idx+1}</td><td>${it.name}</td><td>${it.desc}</td><td><button class="btn btn-sm btn-outline-primary me-1" data-idx="${idx}" data-act="edit">Sửa</button><button class="btn btn-sm btn-outline-danger" data-idx="${idx}" data-act="del">Xóa</button></td></tr>`)); }
  renderWf4();
  $('#wf4OpenModal').on('click', ()=>{ $('#wf4Name').val(''); $('#wf4Desc').val(''); new bootstrap.Modal('#wf4Modal').show(); });
  $('#wf4Save').on('click', ()=>{ const n=$('#wf4Name').val().trim(); if(!n){ alert('Vui lòng nhập Tên'); return;} wf4Items.push({name:n, desc:$('#wf4Desc').val()}); renderWf4(); bootstrap.Modal.getInstance(document.getElementById('wf4Modal')).hide(); });
  $('#wf4Body').on('click','button', function(){ const i=$(this).data('idx'); const act=$(this).data('act'); if(act==='del'){ wf4Items.splice(i,1); renderWf4(); } else { alert('Sửa: '+wf4Items[i].name); } });
  $('#wf4Refresh').on('click', renderWf4);

  // WF5: Stats cards
  const stats=[{title:'Tổng chiến dịch', val:42, color:'primary'},{title:'Đang chạy', val:12, color:'success'},{title:'Tạm dừng', val:7, color:'warning'},{title:'Lỗi', val:3, color:'danger'}];
  const $s=$('#wf5Stats'); stats.forEach(s=> $s.append(`<div class="col-6 col-md-3"><div class="card text-center shadow-sm border-${s.color}"><div class="card-body"><div class="display-6">${s.val}</div><div class="text-${s.color}">${s.title}</div></div></div></div>`));

  // WF6: Multi-select rows
  const wf6Rows = Array.from({length:10}, (_,i)=>({name:`Bản ghi ${i+1}`, type:['A','B'][i%2], date:`2025-08-${(i+1).toString().padStart(2,'0')}`}));
  function renderWf6(){ const $b=$('#wf6Body').empty(); wf6Rows.forEach((r,i)=> $b.append(`<tr><td><input type="checkbox" class="wf6-row"></td><td>${r.name}</td><td>${r.type}</td><td>${r.date}</td></tr>`)); }
  renderWf6();
  $('#wf6CheckAll').on('change', function(){ $('.wf6-row').prop('checked', this.checked); });
  $('#wf6SelectAll').on('click', ()=>$('.wf6-row').prop('checked', true));
  $('#wf6UnselectAll').on('click', ()=>{ $('.wf6-row').prop('checked', false); $('#wf6CheckAll').prop('checked', false); });
  $('#wf6BulkDelete').on('click', ()=>{ const count=$('.wf6-row:checked').length; alert('Xóa '+count+' bản ghi (mô phỏng).'); });

  // WF7: Steps
  $('[data-step]').on('click', function(){ const step=$(this).data('step'); $('.btn-group [data-step]').removeClass('active'); $(this).addClass('active'); $('#wf7Steps .step').addClass('d-none'); $(`#wf7Steps .step[data-step="${step}"]`).removeClass('d-none'); });
  $('#wf7Confirm').on('click', ()=>alert('Đã xác nhận.'));

  // WF8: Upload mock
  $('#wf8Upload').on('click', ()=>{ const files=$('#wf8File')[0].files; const $list=$('#wf8List').empty(); if(!files.length){ alert('Chưa chọn tệp'); return; } Array.from(files).forEach(f=> $list.append(`<li>${f.name} (${Math.round(f.size/1024)} KB)</li>`)); });

  // WF10: Toast & Alert
  $('#wf10ToastBtn').on('click', ()=> new bootstrap.Toast('#wf10Toast').show());
  $('#wf10AlertBtn').on('click', ()=> alert('Đây là cảnh báo mô phỏng.'));
});
</script>
</body>
</html>

