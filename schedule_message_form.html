<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lập <PERSON><PERSON></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        /* Custom styles for select tree */
        .select-tree {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            max-height: 200px;
            overflow-y: auto;
            padding: 0.5rem;
            background-color: #fff;
        }
        
        .tree-item {
            margin: 0.25rem 0;
            padding: 0.25rem;
        }
        
        .tree-item.level-0 {
            margin-left: 0;
        }
        
        .tree-item.level-1 {
            margin-left: 1.5rem;
        }
        
        .tree-item.level-2 {
            margin-left: 3rem;
        }
        
        .tree-toggle {
            cursor: pointer;
            color: #6c757d;
            margin-right: 0.5rem;
        }
        
        .tree-toggle:hover {
            color: #495057;
        }
        
        .variable-tag {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .form-section {
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .section-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1rem;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 0.5rem;
        }
        
        .file-upload-area {
            border: 2px dashed #ced4da;
            border-radius: 0.5rem;
            padding: 1rem;
            text-align: center;
            background-color: #fff;
            transition: all 0.3s ease;
        }
        
        .file-upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        
        .preview-area {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            min-height: 100px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-10">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-6 text-primary">
                        <i class="bi bi-calendar-event me-2"></i>
                        Lập Lịch Gửi Tin Nhắn Tự Động
                    </h1>
                    <p class="text-muted">Tạo và lên lịch gửi tin nhắn tự động đến khách hàng</p>
                </div>

                <form id="scheduleForm" novalidate>
                    <!-- Phần thông tin cơ bản -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-info-circle me-2"></i>
                            Thông Tin Cơ Bản
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="title" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required 
                                       placeholder="Nhập tiêu đề cho chiến dịch">
                                <div class="invalid-feedback">Vui lòng nhập tiêu đề</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="template" class="form-label">Template mẫu</label>
                                <select class="form-select" id="template" name="template">
                                    <option value="">Chọn template mẫu</option>
                                    <option value="welcome">Chào mừng khách hàng mới</option>
                                    <option value="promotion">Khuyến mãi đặc biệt</option>
                                    <option value="reminder">Nhắc nhở thanh toán</option>
                                    <option value="birthday">Chúc mừng sinh nhật</option>
                                    <option value="survey">Khảo sát hài lòng</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="sendDate" class="form-label">Ngày gửi <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="sendDate" name="sendDate" required>
                                <div class="invalid-feedback">Vui lòng chọn ngày gửi</div>
                            </div>
                        </div>
                    </div>

                    <!-- Phần nội dung tin nhắn -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-chat-text me-2"></i>
                            Nội Dung Tin Nhắn
                        </h3>
                        
                        <div class="row">
                            <div class="col-lg-8 mb-3">
                                <label for="messageContent" class="form-label">Nội dung tin nhắn <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="messageContent" name="messageContent" rows="6" required
                                          placeholder="Nhập nội dung tin nhắn..."></textarea>
                                <div class="invalid-feedback">Vui lòng nhập nội dung tin nhắn</div>
                            </div>
                            
                            <div class="col-lg-4">
                                <label class="form-label">Chèn biến động</label>
                                
                                <div class="mb-3">
                                    <label for="dataItem" class="form-label">Biến có sẵn:</label>
                                    <select class="form-select form-select-sm" id="dataItem">
                                        <option value="">Chọn biến</option>
                                        <option value="{ten_khach_hang}">Tên khách hàng</option>
                                        <option value="{so_dien_thoai}">Số điện thoại</option>
                                        <option value="{dia_chi}">Địa chỉ</option>
                                        <option value="{email}">Email</option>
                                        <option value="{ngay_sinh}">Ngày sinh</option>
                                        <option value="{ma_khach_hang}">Mã khách hàng</option>
                                        <option value="{diem_tich_luy}">Điểm tích lũy</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="inputValue" class="form-label">Biến tùy chỉnh:</label>
                                    <input type="text" class="form-control form-control-sm" id="inputValue" 
                                           placeholder="Nhập tên biến">
                                </div>
                                
                                <button type="button" class="btn btn-outline-primary btn-sm w-100" id="insertVariable">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Chèn biến
                                </button>
                            </div>
                        </div>
                        
                        <!-- Preview area -->
                        <div class="mt-3">
                            <label class="form-label">Xem trước nội dung:</label>
                            <div class="preview-area" id="messagePreview">
                                <em class="text-muted">Nội dung tin nhắn sẽ hiển thị ở đây...</em>
                            </div>
                        </div>
                    </div>

                    <!-- Phần Received Outlets (Bộ lọc khách hàng) -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-people me-2"></i>
                            Received Outlets (Bộ Lọc Khách Hàng)
                        </h3>

                        <div class="row">
                            <!-- Organization -->
                            <div class="col-lg-4 mb-4">
                                <label class="form-label">
                                    <i class="bi bi-building me-1"></i>
                                    Organization
                                </label>
                                <div class="select-tree" id="organizationTree">
                                    <div class="tree-item level-0">
                                        <input type="checkbox" id="org_all" class="form-check-input me-2">
                                        <span class="tree-toggle" data-target="org_children">
                                            <i class="bi bi-chevron-down"></i>
                                        </span>
                                        <label for="org_all" class="form-check-label">Tất cả tổ chức</label>

                                        <div id="org_children" class="tree-children">
                                            <div class="tree-item level-1">
                                                <input type="checkbox" id="org_hn" class="form-check-input me-2" data-parent="org_all">
                                                <span class="tree-toggle" data-target="org_hn_children">
                                                    <i class="bi bi-chevron-down"></i>
                                                </span>
                                                <label for="org_hn" class="form-check-label">Hà Nội</label>

                                                <div id="org_hn_children" class="tree-children">
                                                    <div class="tree-item level-2">
                                                        <input type="checkbox" id="org_hn_center" class="form-check-input me-2" data-parent="org_hn">
                                                        <label for="org_hn_center" class="form-check-label">Trung tâm</label>
                                                    </div>
                                                    <div class="tree-item level-2">
                                                        <input type="checkbox" id="org_hn_north" class="form-check-input me-2" data-parent="org_hn">
                                                        <label for="org_hn_north" class="form-check-label">Phía Bắc</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="tree-item level-1">
                                                <input type="checkbox" id="org_hcm" class="form-check-input me-2" data-parent="org_all">
                                                <span class="tree-toggle" data-target="org_hcm_children">
                                                    <i class="bi bi-chevron-down"></i>
                                                </span>
                                                <label for="org_hcm" class="form-check-label">TP.HCM</label>

                                                <div id="org_hcm_children" class="tree-children">
                                                    <div class="tree-item level-2">
                                                        <input type="checkbox" id="org_hcm_center" class="form-check-input me-2" data-parent="org_hcm">
                                                        <label for="org_hcm_center" class="form-check-label">Quận 1</label>
                                                    </div>
                                                    <div class="tree-item level-2">
                                                        <input type="checkbox" id="org_hcm_district7" class="form-check-input me-2" data-parent="org_hcm">
                                                        <label for="org_hcm_district7" class="form-check-label">Quận 7</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Location -->
                            <div class="col-lg-4 mb-4">
                                <label class="form-label">
                                    <i class="bi bi-geo-alt me-1"></i>
                                    Location
                                </label>
                                <div class="select-tree" id="locationTree">
                                    <div class="tree-item level-0">
                                        <input type="checkbox" id="loc_all" class="form-check-input me-2">
                                        <span class="tree-toggle" data-target="loc_children">
                                            <i class="bi bi-chevron-down"></i>
                                        </span>
                                        <label for="loc_all" class="form-check-label">Tất cả địa điểm</label>

                                        <div id="loc_children" class="tree-children">
                                            <div class="tree-item level-1">
                                                <input type="checkbox" id="loc_store" class="form-check-input me-2" data-parent="loc_all">
                                                <span class="tree-toggle" data-target="loc_store_children">
                                                    <i class="bi bi-chevron-down"></i>
                                                </span>
                                                <label for="loc_store" class="form-check-label">Cửa hàng</label>

                                                <div id="loc_store_children" class="tree-children">
                                                    <div class="tree-item level-2">
                                                        <input type="checkbox" id="loc_store_main" class="form-check-input me-2" data-parent="loc_store">
                                                        <label for="loc_store_main" class="form-check-label">Cửa hàng chính</label>
                                                    </div>
                                                    <div class="tree-item level-2">
                                                        <input type="checkbox" id="loc_store_branch" class="form-check-input me-2" data-parent="loc_store">
                                                        <label for="loc_store_branch" class="form-check-label">Chi nhánh</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="tree-item level-1">
                                                <input type="checkbox" id="loc_warehouse" class="form-check-input me-2" data-parent="loc_all">
                                                <label for="loc_warehouse" class="form-check-label">Kho hàng</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Outlet Type -->
                            <div class="col-lg-4 mb-4">
                                <label class="form-label">
                                    <i class="bi bi-shop me-1"></i>
                                    Outlet Type
                                </label>
                                <div class="select-tree" id="outletTypeTree">
                                    <div class="tree-item level-0">
                                        <input type="checkbox" id="type_all" class="form-check-input me-2">
                                        <span class="tree-toggle" data-target="type_children">
                                            <i class="bi bi-chevron-down"></i>
                                        </span>
                                        <label for="type_all" class="form-check-label">Tất cả loại hình</label>

                                        <div id="type_children" class="tree-children">
                                            <div class="tree-item level-1">
                                                <input type="checkbox" id="type_retail" class="form-check-input me-2" data-parent="type_all">
                                                <label for="type_retail" class="form-check-label">Bán lẻ</label>
                                            </div>
                                            <div class="tree-item level-1">
                                                <input type="checkbox" id="type_wholesale" class="form-check-input me-2" data-parent="type_all">
                                                <label for="type_wholesale" class="form-check-label">Bán sỉ</label>
                                            </div>
                                            <div class="tree-item level-1">
                                                <input type="checkbox" id="type_online" class="form-check-input me-2" data-parent="type_all">
                                                <label for="type_online" class="form-check-label">Trực tuyến</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Import Outlets -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="importOutlets">
                                    <label class="form-check-label" for="importOutlets">
                                        <i class="bi bi-upload me-1"></i>
                                        Import outlets từ file
                                    </label>
                                </div>

                                <div id="fileUploadSection" class="d-none">
                                    <div class="file-upload-area">
                                        <i class="bi bi-cloud-upload display-6 text-muted mb-2"></i>
                                        <p class="mb-2">Kéo thả file hoặc click để chọn</p>
                                        <input type="file" class="form-control d-none" id="outletFile" accept=".xlsx,.xls,.csv">
                                        <button type="button" class="btn btn-outline-primary" onclick="$('#outletFile').click()">
                                            <i class="bi bi-folder2-open me-1"></i>
                                            Chọn file
                                        </button>
                                        <small class="text-muted d-block mt-2">
                                            Hỗ trợ file Excel (.xlsx, .xls) và CSV (.csv)
                                        </small>
                                    </div>
                                    <div id="selectedFileName" class="mt-2 text-success d-none">
                                        <i class="bi bi-check-circle me-1"></i>
                                        <span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit buttons -->
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left me-1"></i>
                            Hủy
                        </button>
                        <button type="button" class="btn btn-info me-2" id="previewBtn">
                            <i class="bi bi-eye me-1"></i>
                            Xem trước
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-calendar-check me-1"></i>
                            Lập lịch gửi
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Template mẫu tin nhắn
            const messageTemplates = {
                'welcome': 'Chào mừng {ten_khach_hang} đến với hệ thống của chúng tôi! Cảm ơn bạn đã tin tưởng và lựa chọn dịch vụ của chúng tôi.',
                'promotion': 'Xin chào {ten_khach_hang}! Chúng tôi có chương trình khuyến mãi đặc biệt dành riêng cho bạn. Liên hệ {so_dien_thoai} để biết thêm chi tiết.',
                'reminder': 'Kính gửi {ten_khach_hang}, đây là lời nhắc nhở về khoản thanh toán đến hạn. Vui lòng liên hệ với chúng tôi để được hỗ trợ.',
                'birthday': 'Chúc mừng sinh nhật {ten_khach_hang}! Chúng tôi gửi tặng bạn voucher giảm giá 20% cho lần mua hàng tiếp theo.',
                'survey': 'Xin chào {ten_khach_hang}, chúng tôi rất mong nhận được đánh giá của bạn về dịch vụ. Cảm ơn bạn đã dành thời gian!'
            };

            // Xử lý thay đổi template
            $('#template').change(function() {
                const selectedTemplate = $(this).val();
                if (selectedTemplate && messageTemplates[selectedTemplate]) {
                    $('#messageContent').val(messageTemplates[selectedTemplate]);
                    updatePreview();
                }
            });

            // Xử lý chèn biến
            $('#insertVariable').click(function() {
                const dataItem = $('#dataItem').val();
                const inputValue = $('#inputValue').val().trim();
                const messageContent = $('#messageContent');

                let variableToInsert = '';

                if (dataItem) {
                    variableToInsert = dataItem;
                } else if (inputValue) {
                    variableToInsert = `{${inputValue}}`;
                }

                if (variableToInsert) {
                    const currentContent = messageContent.val();
                    const cursorPos = messageContent[0].selectionStart;
                    const newContent = currentContent.slice(0, cursorPos) + variableToInsert + currentContent.slice(cursorPos);

                    messageContent.val(newContent);

                    // Reset inputs
                    $('#dataItem').val('');
                    $('#inputValue').val('');

                    updatePreview();

                    // Focus back to textarea
                    messageContent.focus();
                    messageContent[0].setSelectionRange(cursorPos + variableToInsert.length, cursorPos + variableToInsert.length);
                }
            });

            // Cập nhật preview khi thay đổi nội dung
            $('#messageContent').on('input', updatePreview);

            // Hàm cập nhật preview
            function updatePreview() {
                const content = $('#messageContent').val();
                let preview = content;

                // Thay thế các biến bằng dữ liệu mẫu
                const sampleData = {
                    '{ten_khach_hang}': 'Nguyễn Văn A',
                    '{so_dien_thoai}': '0123456789',
                    '{dia_chi}': '123 Đường ABC, Quận 1, TP.HCM',
                    '{email}': '<EMAIL>',
                    '{ngay_sinh}': '01/01/1990',
                    '{ma_khach_hang}': 'KH001',
                    '{diem_tich_luy}': '1,250'
                };

                Object.keys(sampleData).forEach(key => {
                    preview = preview.replace(new RegExp(key.replace(/[{}]/g, '\\$&'), 'g'),
                        `<span class="variable-tag">${sampleData[key]}</span>`);
                });

                if (preview.trim() === '') {
                    preview = '<em class="text-muted">Nội dung tin nhắn sẽ hiển thị ở đây...</em>';
                }

                $('#messagePreview').html(preview);
            }

            // Xử lý tree toggle
            $('.tree-toggle').click(function() {
                const target = $(this).data('target');
                const icon = $(this).find('i');
                const children = $('#' + target);

                children.toggle();

                if (children.is(':visible')) {
                    icon.removeClass('bi-chevron-right').addClass('bi-chevron-down');
                } else {
                    icon.removeClass('bi-chevron-down').addClass('bi-chevron-right');
                }
            });

            // Xử lý checkbox parent-child relationship
            $('input[type="checkbox"][data-parent]').change(function() {
                const parentId = $(this).data('parent');
                const parentCheckbox = $('#' + parentId);
                const siblings = $(`input[type="checkbox"][data-parent="${parentId}"]`);

                // Cập nhật trạng thái parent
                const checkedSiblings = siblings.filter(':checked').length;
                const totalSiblings = siblings.length;

                if (checkedSiblings === 0) {
                    parentCheckbox.prop('checked', false).prop('indeterminate', false);
                } else if (checkedSiblings === totalSiblings) {
                    parentCheckbox.prop('checked', true).prop('indeterminate', false);
                } else {
                    parentCheckbox.prop('checked', false).prop('indeterminate', true);
                }
            });

            // Xử lý checkbox parent (select all children)
            $('input[type="checkbox"]:not([data-parent])').change(function() {
                const isChecked = $(this).is(':checked');
                const checkboxId = $(this).attr('id');
                const children = $(`input[type="checkbox"][data-parent="${checkboxId}"]`);

                children.prop('checked', isChecked);
                $(this).prop('indeterminate', false);
            });

            // Xử lý import outlets checkbox
            $('#importOutlets').change(function() {
                if ($(this).is(':checked')) {
                    $('#fileUploadSection').removeClass('d-none');
                } else {
                    $('#fileUploadSection').addClass('d-none');
                    $('#outletFile').val('');
                    $('#selectedFileName').addClass('d-none');
                }
            });

            // Xử lý file upload
            $('#outletFile').change(function() {
                const file = this.files[0];
                if (file) {
                    $('#selectedFileName span').text(file.name);
                    $('#selectedFileName').removeClass('d-none');
                } else {
                    $('#selectedFileName').addClass('d-none');
                }
            });

            // Xử lý drag & drop file upload
            $('.file-upload-area').on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('border-primary bg-light');
            }).on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('border-primary bg-light');
            }).on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('border-primary bg-light');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $('#outletFile')[0].files = files;
                    $('#outletFile').trigger('change');
                }
            });

            // Xử lý form validation
            $('#scheduleForm').on('submit', function(e) {
                e.preventDefault();

                if (this.checkValidity()) {
                    // Thu thập dữ liệu form
                    const formData = {
                        title: $('#title').val(),
                        template: $('#template').val(),
                        sendDate: $('#sendDate').val(),
                        messageContent: $('#messageContent').val(),
                        selectedOrganizations: getSelectedTreeItems('organizationTree'),
                        selectedLocations: getSelectedTreeItems('locationTree'),
                        selectedOutletTypes: getSelectedTreeItems('outletTypeTree'),
                        importOutlets: $('#importOutlets').is(':checked'),
                        outletFile: $('#outletFile')[0].files[0]
                    };

                    console.log('Form Data:', formData);

                    // Hiển thị thông báo thành công
                    alert('Lịch gửi tin nhắn đã được tạo thành công!');
                } else {
                    $(this).addClass('was-validated');
                }
            });

            // Hàm lấy các item được chọn trong tree
            function getSelectedTreeItems(treeId) {
                const selected = [];
                $(`#${treeId} input[type="checkbox"]:checked`).each(function() {
                    const label = $(this).siblings('label').text() || $(this).next('label').text();
                    selected.push({
                        id: $(this).attr('id'),
                        label: label.trim()
                    });
                });
                return selected;
            }

            // Xử lý nút preview
            $('#previewBtn').click(function() {
                const title = $('#title').val() || 'Chưa có tiêu đề';
                const sendDate = $('#sendDate').val() || 'Chưa chọn ngày';
                const content = $('#messageContent').val() || 'Chưa có nội dung';

                const previewHtml = `
                    <div class="modal fade" id="previewModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Xem Trước Tin Nhắn</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <strong>Tiêu đề:</strong> ${title}
                                    </div>
                                    <div class="mb-3">
                                        <strong>Ngày gửi:</strong> ${new Date(sendDate).toLocaleString('vi-VN') || 'Chưa chọn'}
                                    </div>
                                    <div class="mb-3">
                                        <strong>Nội dung:</strong>
                                        <div class="preview-area mt-2">${$('#messagePreview').html()}</div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#previewModal').remove();

                // Add modal to body and show
                $('body').append(previewHtml);
                $('#previewModal').modal('show');
            });

            // Khởi tạo ngày mặc định (ngày mai)
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowString = tomorrow.toISOString().slice(0, 16);
            $('#sendDate').val(tomorrowString);

            // Khởi tạo preview ban đầu
            updatePreview();
        });
    </script>
</body>
</html>
